/**
 * 认证状态调试工具
 * 用于检查和调试登录状态的持久化问题
 */

import { STORAGE_KEYS } from '@/constants';

export const authDebug = {
  /**
   * 检查所有存储状态
   */
  checkAllStorage: () => {
    console.group('🔍 认证状态检查');
    
    // 检查 localStorage
    console.log('📦 localStorage:');
    console.log('  token:', localStorage.getItem(STORAGE_KEYS.TOKEN));
    console.log('  user:', localStorage.getItem(STORAGE_KEYS.USER));
    
    // 检查 zustand persist 存储
    console.log('🗄️ zustand persist:');
    const zustandAuth = localStorage.getItem('pavcore-auth');
    if (zustandAuth) {
      try {
        const parsed = JSON.parse(zustandAuth);
        console.log('  parsed state:', parsed);
        console.log('  token:', parsed.state?.token);
        console.log('  refreshToken:', parsed.state?.refreshTokenValue);
        console.log('  isAuthenticated:', parsed.state?.isAuthenticated);
        console.log('  user:', parsed.state?.user);
      } catch (e) {
        console.log('  解析失败:', e);
      }
    } else {
      console.log('  无 zustand 持久化数据');
    }
    
    console.groupEnd();
  },

  /**
   * 清理所有认证相关存储
   */
  clearAllAuth: () => {
    console.log('🧹 清理所有认证状态');
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
    localStorage.removeItem('pavcore-auth');
    console.log('✅ 清理完成');
  },

  /**
   * 模拟登录状态
   */
  mockLoginState: () => {
    const mockToken = 'mock-jwt-token-' + Date.now();
    const mockUser = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      name: '测试用户'
    };

    // 设置 localStorage
    localStorage.setItem(STORAGE_KEYS.TOKEN, mockToken);
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(mockUser));

    // 设置 zustand persist
    const zustandState = {
      state: {
        token: mockToken,
        refreshTokenValue: 'mock-refresh-token-' + Date.now(),
        user: mockUser,
        isAuthenticated: true,
        permissions: ['read', 'write'],
        menus: [],
        encryptionSupported: true
      },
      version: 0
    };
    localStorage.setItem('pavcore-auth', JSON.stringify(zustandState));

    console.log('🎭 模拟登录状态已设置');
    authDebug.checkAllStorage();
  },

  /**
   * 检查状态一致性
   */
  checkConsistency: () => {
    console.group('🔄 状态一致性检查');
    
    const localToken = localStorage.getItem(STORAGE_KEYS.TOKEN);
    const zustandAuth = localStorage.getItem('pavcore-auth');
    
    let zustandToken = null;
    let zustandAuthenticated = false;
    
    if (zustandAuth) {
      try {
        const parsed = JSON.parse(zustandAuth);
        zustandToken = parsed.state?.token;
        zustandAuthenticated = parsed.state?.isAuthenticated;
      } catch (e) {
        console.error('解析 zustand 状态失败:', e);
      }
    }
    
    console.log('localStorage token:', localToken);
    console.log('zustand token:', zustandToken);
    console.log('zustand isAuthenticated:', zustandAuthenticated);
    
    const isConsistent = localToken === zustandToken;
    console.log(isConsistent ? '✅ 状态一致' : '❌ 状态不一致');
    
    if (!isConsistent) {
      console.warn('⚠️ 检测到状态不一致，可能导致刷新后登录状态丢失');
    }
    
    console.groupEnd();
    return isConsistent;
  }
};

// 在开发环境下将调试工具挂载到 window 对象
if (import.meta.env.DEV) {
  (window as any).authDebug = authDebug;
  console.log('🛠️ 认证调试工具已挂载到 window.authDebug');
}
