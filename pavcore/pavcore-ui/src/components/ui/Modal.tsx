import React, { useEffect, useRef, forwardRef } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/utils/cn';
import { IconButton } from './IconButton';

/**
 * Modal组件的属性接口
 */
export interface ModalProps {
  /** 是否显示Modal */
  open: boolean;
  /** 关闭Modal的回调 */
  onClose: () => void;
  /** Modal标题 */
  title?: string;
  /** Modal尺寸 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** 是否显示关闭按钮 */
  showCloseButton?: boolean;
  /** 是否点击遮罩层关闭 */
  closeOnOverlayClick?: boolean;
  /** 是否按ESC键关闭 */
  closeOnEscape?: boolean;
  /** 是否阻止滚动 */
  preventScroll?: boolean;
  /** 自定义CSS类名 */
  className?: string;
  /** 遮罩层CSS类名 */
  overlayClassName?: string;
  /** 内容区域CSS类名 */
  contentClassName?: string;
  /** 子元素 */
  children: React.ReactNode;
}

/**
 * 现代化的Modal组件
 * 
 * 特性：
 * - 平滑的进入/退出动画（300ms ease-out）
 * - 多种尺寸选择
 * - 键盘导航支持（ESC关闭、焦点管理）
 * - 点击遮罩层关闭
 * - 滚动锁定
 * - 无障碍访问优化
 * - 响应式设计和暗色模式支持
 * 
 * 动画时序：
 * - 进入动画：300ms ease-out
 * - 退出动画：300ms ease-out
 * - 与其他组件保持一致的时序
 * 
 * @param props Modal组件属性
 * @returns React函数组件
 */
export const Modal = forwardRef<HTMLDivElement, ModalProps>(({
  open,
  onClose,
  title,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  preventScroll = true,
  className,
  overlayClassName,
  contentClassName,
  children,
}, ref) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // 尺寸样式
  const sizeClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full mx-4',
  };

  // 处理ESC键关闭
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (closeOnEscape && event.key === 'Escape') {
        onClose();
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [open, closeOnEscape, onClose]);

  // 焦点管理
  useEffect(() => {
    if (open) {
      // 保存当前焦点元素
      previousActiveElement.current = document.activeElement as HTMLElement;
      
      // 将焦点移到Modal
      setTimeout(() => {
        modalRef.current?.focus();
      }, 100);
    } else {
      // 恢复之前的焦点
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    }
  }, [open]);

  // 滚动锁定
  useEffect(() => {
    if (preventScroll && open) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';
      
      return () => {
        document.body.style.overflow = originalStyle;
      };
    }
  }, [open, preventScroll]);

  // 处理遮罩层点击
  const handleOverlayClick = (event: React.MouseEvent) => {
    // 只有在启用点击外部关闭时才处理
    if (closeOnOverlayClick) {
      event.preventDefault();
      event.stopPropagation();
      onClose();
    }
  };

  if (!open) return null;

  return (
    <div
      className={cn(
        // 基于shadcn/ui，优化为更大气的遮罩层
        'fixed inset-0 z-50',
        'flex items-center justify-center p-4 sm:p-6',
        // 更现代的渐变背景
        'bg-gradient-to-br from-slate-900/70 via-slate-900/80 to-black/90',
        'dark:from-black/80 dark:via-black/90 dark:to-slate-900/95',
        // shadcn/ui风格的动画系统 - 优化关闭时序
        'data-[state=open]:animate-in data-[state=closed]:animate-out',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
        'data-[state=closed]:duration-200 data-[state=open]:duration-300',
        overlayClassName
      )}
      data-state={open ? 'open' : 'closed'}
      data-modal-overlay
      style={{
        zIndex: 1300
      }}
      onClick={handleOverlayClick}
      aria-label="点击关闭弹窗"
    >

      {/* Modal内容 */}
      <div
        ref={ref || modalRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'modal-title' : undefined}
        tabIndex={-1}
        data-state={open ? 'open' : 'closed'}
        data-modal-content
        onClick={(e) => e.stopPropagation()}
        className={cn(
          // 使用专门的CSS类确保内容重置
          'modal-content-reset',

          // 现代Modal设计 - 纯净无边框
          'relative bg-white dark:bg-slate-900',
          'max-h-[90vh] overflow-hidden',
          'focus:outline-none',

          // 现代圆角
          'rounded-2xl',

          // 高级阴影系统
          'shadow-[0_32px_64px_-12px_rgba(0,0,0,0.25)]',
          'dark:shadow-[0_32px_64px_-12px_rgba(0,0,0,0.4)]',

          // 微妙的边框效果
          'ring-1 ring-slate-900/5 dark:ring-white/10',

          // 尺寸控制
          sizeClasses[size],
          'w-full mx-4',

          // 从下向上浮出的动画效果
          'modal-slide-up',

          className
        )}
      >
        {/* 头部 */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-700">
            {title && (
              <h2
                id="modal-title"
                className="text-lg font-semibold text-slate-900 dark:text-slate-100"
              >
                {title}
              </h2>
            )}
            
            {showCloseButton && (
              <IconButton
                variant="ghost"
                size="sm"
                icon={<X />}
                onClick={onClose}
                tooltip="关闭"
                className="ml-auto"
              />
            )}
          </div>
        )}
        
        {/* 内容区域 */}
        <div
          className={cn(
            'overflow-y-auto',
            title || showCloseButton ? 'p-6' : 'p-6',
            contentClassName
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
});

Modal.displayName = 'Modal';

export default Modal;
