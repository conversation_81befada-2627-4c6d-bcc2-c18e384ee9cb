import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/stores/auth';
import { authDebug } from '@/utils/authDebug';

const AuthDebugPage: React.FC = () => {
  const { 
    user, 
    token, 
    isAuthenticated, 
    loading, 
    permissions, 
    menus,
    refreshTokenValue,
    initialize,
    logout
  } = useAuthStore();

  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    addLog('组件已挂载');
  }, []);

  const handleCheckStorage = () => {
    authDebug.checkAllStorage();
    addLog('已检查存储状态（查看控制台）');
  };

  const handleCheckConsistency = () => {
    const isConsistent = authDebug.checkConsistency();
    addLog(`状态一致性检查: ${isConsistent ? '一致' : '不一致'}`);
  };

  const handleClearAll = () => {
    authDebug.clearAllAuth();
    addLog('已清理所有认证状态');
  };

  const handleMockLogin = () => {
    authDebug.mockLoginState();
    addLog('已设置模拟登录状态');
  };

  const handleReinitialize = async () => {
    addLog('开始重新初始化...');
    try {
      await initialize();
      addLog('重新初始化完成');
    } catch (error) {
      addLog(`重新初始化失败: ${error}`);
    }
  };

  const handleLogout = () => {
    logout();
    addLog('已执行登出');
  };

  const handleRefreshPage = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8">
          认证状态调试页面
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 当前状态 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
              当前认证状态
            </h2>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">加载中:</span>
                <span className={loading ? 'text-yellow-600' : 'text-green-600'}>
                  {loading ? '是' : '否'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">已认证:</span>
                <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                  {isAuthenticated ? '是' : '否'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">有Token:</span>
                <span className={token ? 'text-green-600' : 'text-red-600'}>
                  {token ? '是' : '否'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">有RefreshToken:</span>
                <span className={refreshTokenValue ? 'text-green-600' : 'text-red-600'}>
                  {refreshTokenValue ? '是' : '否'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">用户:</span>
                <span className="text-gray-900 dark:text-gray-100">
                  {user ? user.username || user.name || '未知' : '无'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">权限数量:</span>
                <span className="text-gray-900 dark:text-gray-100">
                  {permissions.length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">菜单数量:</span>
                <span className="text-gray-900 dark:text-gray-100">
                  {menus.length}
                </span>
              </div>
            </div>
          </div>

          {/* 调试操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
              调试操作
            </h2>
            <div className="space-y-3">
              <button
                onClick={handleCheckStorage}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                检查存储状态
              </button>
              <button
                onClick={handleCheckConsistency}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
              >
                检查状态一致性
              </button>
              <button
                onClick={handleMockLogin}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                设置模拟登录状态
              </button>
              <button
                onClick={handleReinitialize}
                className="w-full px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
              >
                重新初始化
              </button>
              <button
                onClick={handleLogout}
                className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                执行登出
              </button>
              <button
                onClick={handleClearAll}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                清理所有状态
              </button>
              <button
                onClick={handleRefreshPage}
                className="w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors"
              >
                刷新页面
              </button>
            </div>
          </div>
        </div>

        {/* 操作日志 */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
            操作日志
          </h2>
          <div className="bg-gray-100 dark:bg-gray-700 rounded p-4 h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">暂无日志</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono text-gray-800 dark:text-gray-200">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
          <button
            onClick={() => setLogs([])}
            className="mt-2 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
          >
            清空日志
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthDebugPage;
