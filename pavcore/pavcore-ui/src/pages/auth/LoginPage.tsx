import React from 'react';
import { Sun, Moon } from 'lucide-react';
import { useThemeStore } from '@/stores/theme';
import { EncryptedLoginForm } from '@/components/auth/EncryptedLoginForm';

export const LoginPage: React.FC = () => {
  const { theme, toggleTheme } = useThemeStore();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      {/* 主卡片容器：左右分区 */}
      <div className="flex flex-col lg:flex-row bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden max-w-4xl w-full">
        {/* 左侧图片区 */}
        <div
          className="hidden lg:block lg:w-1/2 bg-cover bg-center relative"
          style={{ backgroundImage: 'url(/src/assets/img/login-pic.jpg)' }}
        >
          {/* Logo */}
          <div className="absolute top-4 left-4 z-10">
            <div className="flex items-center space-x-2">
              <img
                src="/src/assets/img/logo.svg"
                alt="Logo"
                className="h-8 invert"
              />
              <span className="text-2xl font-bold text-white">PavCore</span>
            </div>
          </div>
          
          {/* 遮罩 */}
          <div className="absolute inset-0 bg-black opacity-40"></div>
          
          {/* 底部引用 */}
          <div className="absolute bottom-8 left-8 text-white">
            <p className="text-xl">
              "Simply all the tools that my team and I need."
            </p>
            <p className="mt-2 mr-5 text-sm text-right">— Karen Yue</p>
          </div>
        </div>

        {/* 右侧登录区 */}
        <div className="w-full lg:w-1/2 p-8 relative">
          {/* 主题切换按钮 */}
          <div className="absolute top-4 right-4">
            <button
              onClick={toggleTheme}
              className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              aria-label="切换主题"
            >
              {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
            </button>
          </div>

          {/* 登录内容区 */}
          <div className="mt-8 space-y-6">
            {/* 标题 */}
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                欢迎使用 PavCore
              </h2>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                管理更轻松，操作更高效
              </p>
            </div>

            {/* 加密登录表单 */}
            <EncryptedLoginForm />

            {/* 分隔线 */}
            <div className="flex items-center justify-center space-x-3">
              <hr className="flex-grow border-gray-300 dark:border-gray-600" />
              <span className="text-gray-400 dark:text-gray-500 text-sm">其他选项</span>
              <hr className="flex-grow border-gray-300 dark:border-gray-600" />
            </div>

            {/* 注册链接 */}
            <p className="text-center text-gray-500 dark:text-gray-400 text-sm">
              没有账号？{' '}
              <a href="#" className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium">
                注册新账号
              </a>
            </p>

            {/* 版权信息 */}
            <div className="text-center text-xs text-gray-400 dark:text-gray-500 mt-8">
              <p>© 2024 PavCore. All rights reserved.</p>
              <p className="mt-1">
                <a href="#" className="hover:text-gray-600 dark:hover:text-gray-300">隐私政策</a>
                {' · '}
                <a href="#" className="hover:text-gray-600 dark:hover:text-gray-300">服务条款</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
