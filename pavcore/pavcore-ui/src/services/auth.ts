import http from './http';
import type {
  LoginCredentials,
  EncryptedLoginCredentials,
  LoginResponse,
  PublicKeyResponse,
  RefreshTokenRequest,
  ChangePasswordRequest,
  ApiResponse,
  User
} from '@/types';

export const authService = {
  // Get RSA public key
  getPublicKey: () =>
    http.get<ApiResponse<PublicKeyResponse>>('/auth/public-key'),

  // Encrypted login (primary method)
  encryptedLogin: (credentials: EncryptedLoginCredentials) =>
    http.post<ApiResponse<LoginResponse>>('/auth/login/encrypted', credentials),

  // Plain login (fallback method)
  login: (credentials: LoginCredentials) =>
    http.post<ApiResponse<LoginResponse>>('/auth/login', credentials),

  // Logout
  logout: () =>
    http.post('/auth/logout'),

  // Get current user info
  getCurrentUser: () =>
    http.get<any>('/auth/me'),

  // Refresh token
  refreshToken: (request: RefreshTokenRequest) =>
    http.post<{ accessToken: string; tokenType: string; expiresIn: number }>('/auth/refresh', request),

  // Change password
  changePassword: (data: ChangePasswordRequest) =>
    http.post('/auth/change-password', data),

  // Get user permissions
  getUserPermissions: () =>
    http.get<string[]>('/auth/permissions'),

  // Get user menus
  getUserMenus: () =>
    http.get('/auth/menus'),
};
