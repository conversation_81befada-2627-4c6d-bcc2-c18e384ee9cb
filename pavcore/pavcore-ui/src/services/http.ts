import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_BASE_URL, STORAGE_KEYS } from '@/constants';
import type { ApiResponse } from '@/types';

class HttpClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 开发环境下模拟后端响应
        if (import.meta.env.DEV && this.shouldMockRequest(config)) {
          return this.mockRequest(config);
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid - 清理所有认证状态
          localStorage.removeItem(STORAGE_KEYS.TOKEN);
          localStorage.removeItem(STORAGE_KEYS.USER);

          // 清理 zustand persist 存储
          localStorage.removeItem('pavcore-auth');

          // 重定向到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }

        // Handle other errors
        const message = error.response?.data?.message || error.message || 'An error occurred';
        return Promise.reject(new Error(message));
      }
    );
  }

  private shouldMockRequest(config: any): boolean {
    // 检查是否应该模拟请求（当后端不可用时）
    const mockEndpoints = ['/auth/me', '/auth/permissions', '/auth/menus'];
    return mockEndpoints.some(endpoint => config.url?.includes(endpoint));
  }

  private mockRequest(config: any): Promise<any> {
    // 模拟后端响应
    return new Promise((resolve) => {
      setTimeout(() => {
        if (config.url?.includes('/auth/me')) {
          const mockUserResponse = {
            status: 'success',
            message: '获取用户信息成功',
            data: {
              userId: 1,
              username: 'testuser',
              email: '<EMAIL>',
              nickname: '测试用户',
              avatar: '',
              phone: '13800138000',
              deptId: 1,
              deptName: '技术部',
              roles: ['USER'],
              permissions: ['read', 'write'],
              dataScope: 'ALL'
            }
          };
          resolve({ data: mockUserResponse });
        } else if (config.url?.includes('/auth/permissions')) {
          resolve({
            data: {
              status: 'success',
              data: ['read', 'write', 'admin']
            }
          });
        } else if (config.url?.includes('/auth/menus')) {
          resolve({
            data: {
              status: 'success',
              data: []
            }
          });
        } else {
          resolve({ data: { status: 'success', data: null } });
        }
      }, 100); // 模拟网络延迟
    });
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  // Upload file
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  }
}

export const http = new HttpClient();
export default http;
