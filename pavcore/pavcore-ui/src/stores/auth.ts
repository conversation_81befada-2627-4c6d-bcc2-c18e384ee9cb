import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  User,
  LoginCredentials,
  EncryptedLoginCredentials,
  LoginResponse,
  PublicKeyResponse,
  Menu,
  PublicKeyCache
} from '@/types';
import { authService } from '@/services/auth';
import { STORAGE_KEYS } from '@/constants';
import { RSAEncryption } from '@/utils/crypto';
import { TimeSync } from '@/utils/timeSync';

// 辅助函数：检查公钥缓存是否有效
const isPublicKeyCacheValid = (cache: PublicKeyCache): boolean => {
  const now = Date.now();
  const keyAge = now - cache.keyTimestamp;
  const maxAge = cache.validHours * 60 * 60 * 1000;

  // 检查时间有效性
  const timeValid = keyAge < maxAge;

  // 检查格式有效性
  const formatValid = RSAEncryption.validatePublicKey(cache.publicKey);

  // 检查缓存时间（防止缓存过久）
  const cacheAge = now - cache.cachedAt;
  const maxCacheAge = Math.min(maxAge, 23 * 60 * 60 * 1000); // 最多缓存23小时
  const cacheValid = cacheAge < maxCacheAge;

  return timeValid && formatValid && cacheValid;
};

interface AuthState {
  user: User | null;
  token: string | null;
  refreshTokenValue: string | null;
  permissions: string[];
  menus: Menu[];
  isAuthenticated: boolean;
  loading: boolean;
  publicKeyCache: PublicKeyCache | null;
  encryptionSupported: boolean;
}

interface AuthActions {
  // 加密登录（主要方法）
  encryptedLogin: (credentials: LoginCredentials) => Promise<void>;
  // 普通登录（降级方法）
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  setUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
  // 公钥管理
  setPublicKeyCache: (cache: PublicKeyCache | null) => void;
  checkEncryptionSupport: () => boolean;
}

// 创建一个函数来获取初始状态
const getInitialState = (): AuthState => {
  // 检查是否有持久化的认证状态
  const persistedAuth = localStorage.getItem('pavcore-auth');
  if (persistedAuth) {
    try {
      const parsed = JSON.parse(persistedAuth);
      const state = parsed.state;
      if (state && state.token && state.user) {
        console.log('🔄 检测到持久化的认证状态，初始化为已认证');
        return {
          user: state.user,
          token: state.token,
          refreshTokenValue: state.refreshTokenValue,
          permissions: state.permissions || [],
          menus: state.menus || [],
          isAuthenticated: true, // 关键：初始化为已认证
          loading: false, // 关键：不显示加载状态
          publicKeyCache: null,
          encryptionSupported: state.encryptionSupported || true,
        };
      }
    } catch (error) {
      console.warn('解析持久化状态失败:', error);
    }
  }

  // 默认状态
  return {
    user: null,
    token: null,
    refreshTokenValue: null,
    permissions: [],
    menus: [],
    isAuthenticated: false,
    loading: true,
    publicKeyCache: null,
    encryptionSupported: true,
  };
};

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // 使用动态初始状态
      ...getInitialState(),

      // Actions

      // 加密登录（主要登录方法）
      encryptedLogin: async (credentials: LoginCredentials) => {
        try {
          set({ loading: true });

          // 检查加密支持
          if (!get().checkEncryptionSupport()) {
            console.warn('浏览器不支持RSA加密，降级到普通登录');
            return await get().login(credentials);
          }

          try {
            // 1. 获取有效公钥（使用缓存或获取新的）
            let publicKeyData: PublicKeyCache;
            const currentCache = get().publicKeyCache;

            // 检查缓存是否有效
            if (currentCache && isPublicKeyCacheValid(currentCache)) {
              console.log('使用缓存的公钥进行加密登录');
              publicKeyData = currentCache;
            } else {
              console.log('获取新公钥进行加密登录');
              const publicKeyResponse = await authService.getPublicKey();

              console.log('公钥API响应:', publicKeyResponse);
              console.log('响应数据:', publicKeyResponse.data);

              // 检查公钥响应
              if (!publicKeyResponse.data || publicKeyResponse.status === 'error') {
                console.error('公钥响应检查失败:', {
                  hasData: !!publicKeyResponse.data,
                  status: publicKeyResponse.status
                });
                throw new Error(publicKeyResponse.message || '获取公钥失败');
              }

              const newKeyData = publicKeyResponse.data as unknown as PublicKeyResponse;

              // 验证公钥格式
              if (!RSAEncryption.validatePublicKey(newKeyData.publicKey)) {
                throw new Error('服务器返回的公钥无效');
              }

              // 同步服务器时间
              TimeSync.updateServerTime(newKeyData.serverTimestamp);

              // 创建缓存数据
              publicKeyData = {
                ...newKeyData,
                cachedAt: Date.now()
              };

              // 缓存公钥
              set({ publicKeyCache: publicKeyData });
            }

            // 2. 加密密码
            const rsa = new RSAEncryption(publicKeyData.publicKey);
            const encryptedPassword = rsa.encrypt(credentials.password);

            // 3. 准备加密登录请求
            const encryptedCredentials: EncryptedLoginCredentials = {
              username: credentials.username,
              encryptedPassword,
              timestamp: TimeSync.getServerTime()
            };

            console.log('发送加密登录请求:', {
              username: encryptedCredentials.username,
              timestamp: encryptedCredentials.timestamp,
              encryptedPasswordLength: encryptedPassword.length
            });

            // 4. 发送加密登录请求
            const response = await authService.encryptedLogin(encryptedCredentials);

            // 检查响应状态
            if (!response.data || response.status === 'error') {
              throw new Error(response.message || '登录失败');
            }

            const { accessToken, refreshToken, userInfo } = response.data as unknown as LoginResponse;

            // 转换用户信息格式
            const user: User = {
              id: userInfo.userId || userInfo.id,
              username: userInfo.username,
              email: userInfo.email,
              nickname: userInfo.nickname,
              avatar: userInfo.avatar,
              phone: userInfo.phone,
              gender: userInfo.gender,
              enabled: true,
              department: userInfo.deptId ? {
                id: userInfo.deptId,
                name: userInfo.deptName,
                code: '',
                children: [],
                sortOrder: 0,
                enabled: true,
                createdAt: '',
                updatedAt: ''
              } : undefined,
              roles: userInfo.roles || [],
              createdAt: '',
              updatedAt: ''
            };

            // 8. 保存认证信息到 localStorage 和 zustand
            localStorage.setItem(STORAGE_KEYS.TOKEN, accessToken);

            set({
              user,
              token: accessToken,
              refreshTokenValue: refreshToken,
              permissions: [], // 将在后续获取
              menus: [], // 将在后续获取
              isAuthenticated: true,
              loading: false,
            });

            // 9. 获取用户权限和菜单（异步）
            try {
              const [permissionsResponse, menusResponse] = await Promise.all([
                authService.getUserPermissions(),
                authService.getUserMenus(),
              ]);

              set({
                permissions: permissionsResponse.data || [],
                menus: menusResponse.data,
              });
            } catch (permError) {
              console.warn('获取用户权限或菜单失败:', permError);
              // 不影响登录流程
            }

            console.log('加密登录成功');
          } catch (encryptError) {
            console.error('加密登录失败:', encryptError);
            // 不降级，直接抛出错误让上层处理
            throw encryptError;
          }
        } catch (error) {
          set({ loading: false });
          throw error;
        }
      },

      // 普通登录（降级方法）
      login: async (credentials: LoginCredentials) => {
        try {
          set({ loading: true });
          console.log('使用普通登录方式');

          // 调用普通登录API
          const response = await authService.login(credentials);

          // 检查响应状态
          if (!response.data || response.status === 'error') {
            throw new Error(response.message || '登录失败');
          }

          const { accessToken, refreshToken, userInfo } = response.data as unknown as LoginResponse;

          // 转换用户信息格式
          const user: User = {
            id: userInfo.userId || userInfo.id,
            username: userInfo.username,
            email: userInfo.email,
            nickname: userInfo.nickname,
            avatar: userInfo.avatar,
            phone: userInfo.phone,
            gender: userInfo.gender,
            enabled: true,
            department: userInfo.deptId ? {
              id: userInfo.deptId,
              name: userInfo.deptName,
              code: '',
              children: [],
              sortOrder: 0,
              enabled: true,
              createdAt: '',
              updatedAt: ''
            } : undefined,
            roles: userInfo.roles || [],
            createdAt: '',
            updatedAt: ''
          };

          // 保存认证信息到 localStorage 和 zustand
          localStorage.setItem(STORAGE_KEYS.TOKEN, accessToken);

          set({
            user,
            token: accessToken,
            refreshTokenValue: refreshToken,
            permissions: [], // 将在后续获取
            menus: [], // 将在后续获取
            isAuthenticated: true,
            loading: false,
          });

          // 获取用户权限和菜单（异步）
          try {
            const [permissionsResponse, menusResponse] = await Promise.all([
              authService.getUserPermissions(),
              authService.getUserMenus(),
            ]);

            set({
              permissions: permissionsResponse.data || [],
              menus: menusResponse.data || [],
            });
          } catch (permError) {
            console.warn('获取用户权限或菜单失败:', permError);
            // 不影响登录流程
          }

          console.log('普通登录成功');
        } catch (error) {
          set({ loading: false });
          throw error;
        }
      },

      logout: () => {
        authService.logout().catch(() => {
          // Ignore logout errors
        });

        // 清除所有认证相关状态
        localStorage.removeItem(STORAGE_KEYS.TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER); // 也清除用户信息

        set({
          user: null,
          token: null,
          refreshTokenValue: null,
          permissions: [],
          menus: [],
          isAuthenticated: false,
          loading: false,
          publicKeyCache: null, // 清除公钥缓存
        });
      },

      refreshToken: async () => {
        try {
          const currentRefreshToken = get().refreshTokenValue;
          if (!currentRefreshToken) {
            throw new Error('没有可用的刷新令牌');
          }

          const response = await authService.refreshToken({ refreshToken: currentRefreshToken });
          const { accessToken } = response.data!;

          // 更新token
          localStorage.setItem(STORAGE_KEYS.TOKEN, accessToken);
          set({ token: accessToken });
        } catch (error) {
          console.error('刷新令牌失败:', error);
          get().logout();
          throw error;
        }
      },

      getCurrentUser: async () => {
        try {
          const response = await authService.getCurrentUser();
          console.log('🔍 getCurrentUser 响应:', response);

          // 后端返回的是 Map<String, Object>，需要转换为 User 类型
          const userData = response.data;
          if (userData) {
            // 转换后端返回的数据格式为前端期望的 User 类型
            const user: User = {
              id: userData.userId || userData.id,
              username: userData.username,
              email: userData.email,
              nickname: userData.nickname,
              avatar: userData.avatar,
              phone: userData.phone,
              gender: userData.gender,
              enabled: true, // 能获取到用户信息说明用户是启用的
              department: userData.deptId ? {
                id: userData.deptId,
                name: userData.deptName,
                code: '',
                children: [],
                sortOrder: 0,
                enabled: true,
                createdAt: '',
                updatedAt: ''
              } : undefined,
              roles: userData.roles || [],
              createdAt: '',
              updatedAt: ''
            };

            set({ user });
            console.log('✅ 用户信息已更新:', user);
          }
        } catch (error) {
          console.error('❌ 获取用户信息失败:', error);
          get().logout();
          throw error;
        }
      },

      hasPermission: (permission: string) => {
        const { permissions } = get();
        return permissions.includes(permission);
      },

      hasAnyPermission: (permissions: string[]) => {
        const { permissions: userPermissions } = get();
        return permissions.some(permission => userPermissions.includes(permission));
      },

      hasAllPermissions: (permissions: string[]) => {
        const { permissions: userPermissions } = get();
        return permissions.every(permission => userPermissions.includes(permission));
      },

      setUser: (user: User) => {
        set({ user });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // 公钥管理方法
      setPublicKeyCache: (cache: PublicKeyCache | null) => {
        set({ publicKeyCache: cache });
      },

      // 检查浏览器是否支持RSA加密
      checkEncryptionSupport: () => {
        try {
          const supported = RSAEncryption.isSupported();
          set({ encryptionSupported: supported });
          return supported;
        } catch (error) {
          console.warn('检查加密支持失败:', error);
          set({ encryptionSupported: false });
          return false;
        }
      },

      initialize: async () => {
        try {
          console.log('🚀 开始初始化认证状态');

          // 检查加密支持
          get().checkEncryptionSupport();

          const currentState = get();
          console.log('📊 当前状态:', {
            hasToken: !!currentState.token,
            isAuthenticated: currentState.isAuthenticated,
            hasUser: !!currentState.user,
            loading: currentState.loading
          });

          // 如果已经是认证状态（从持久化恢复），只需要后台验证
          if (currentState.isAuthenticated && currentState.token && currentState.user) {
            console.log('✅ 已有认证状态，进行后台验证');

            // 异步验证 token 有效性，不阻塞 UI
            setTimeout(async () => {
              try {
                await get().getCurrentUser();
                console.log('✅ 后台验证成功');
              } catch (error) {
                console.warn('❌ 后台验证失败，清除状态:', error);
                get().logout();
              }
            }, 100);
            return;
          }

          // 检查是否有 token 但没有用户信息
          const token = currentState.token || localStorage.getItem(STORAGE_KEYS.TOKEN);
          if (token && !currentState.user) {
            console.log('🔍 有 token 但无用户信息，进行验证');

            try {
              // 同步 token
              if (token !== currentState.token) {
                set({ token });
                localStorage.setItem(STORAGE_KEYS.TOKEN, token);
              }

              // 获取用户信息
              await get().getCurrentUser();

              // 获取权限和菜单
              const [permissionsResponse, menusResponse] = await Promise.all([
                authService.getUserPermissions(),
                authService.getUserMenus(),
              ]);

              set({
                permissions: permissionsResponse.data || [],
                menus: menusResponse.data || [],
                isAuthenticated: true,
                loading: false,
              });

              console.log('✅ 验证成功，设置为认证状态');
            } catch (error) {
              console.warn('❌ 验证失败，清除状态:', error);
              localStorage.removeItem(STORAGE_KEYS.TOKEN);
              set({
                loading: false,
                isAuthenticated: false,
                token: null,
                user: null,
                permissions: [],
                menus: [],
                refreshTokenValue: null
              });
            }
          } else {
            // 没有 token，设置为未认证状态
            console.log('❌ 没有 token，设置为未认证状态');
            set({
              loading: false,
              isAuthenticated: false
            });
          }
        } catch (error) {
          console.error('💥 初始化错误:', error);
          set({
            loading: false,
            isAuthenticated: false
          });
        }
      },
    }),
    {
      name: 'pavcore-auth',
      partialize: (state) => ({
        // 只持久化必要的状态，公钥缓存不持久化（安全考虑）
        token: state.token,
        refreshTokenValue: state.refreshTokenValue,
        user: state.user,
        permissions: state.permissions,
        menus: state.menus,
        isAuthenticated: state.isAuthenticated,
        encryptionSupported: state.encryptionSupported,
      }),
      onRehydrateStorage: () => (state, error) => {
        console.log('🔄 Zustand persist 恢复完成:', state ? '成功' : '失败', error);
      },
    }
  )
);
