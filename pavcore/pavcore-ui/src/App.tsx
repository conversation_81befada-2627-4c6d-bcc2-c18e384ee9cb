import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuthStore } from '@/stores/auth';
import { useThemeStore } from '@/stores/theme';
import AuthGuard from '@/components/common/AuthGuard';
import AppLoadingScreen from '@/components/common/AppLoadingScreen';
import TestComponent from '@/components/common/TestComponent';
import FormFieldExample from '@/components/examples/FormFieldExample';
import CardExample from '@/components/examples/CardExample';
import ButtonExample from '@/components/examples/ButtonExample';
import ModalExample from '@/components/examples/ModalExample';
import DropdownExample from '@/components/examples/DropdownExample';
import ComponentsPage from '@/pages/components/ComponentsPage';
import LoginPage from '@/pages/auth/LoginPage';
import MainLayout from '@/components/layout/MainLayout';
import DashboardPage from '@/pages/dashboard/DashboardPage';
import AuthDebugPage from '@/pages/debug/AuthDebugPage';
import ToastContainer from '@/components/ui/Toast';
import '@/i18n';

// 开发环境下引入认证调试工具
if (import.meta.env.DEV) {
  import('@/utils/authDebug');
}

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const { initialize, loading } = useAuthStore();
  const { theme } = useThemeStore();
  const [showMainContent, setShowMainContent] = useState(false);

  useEffect(() => {
    // Initialize auth state
    initialize();
  }, [initialize]);

  useEffect(() => {
    // Apply theme on mount
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(theme);
  }, [theme]);

  useEffect(() => {
    // 当loading完成时，延迟显示主内容以确保平滑过渡
    if (!loading) {
      const timer = setTimeout(() => {
        setShowMainContent(true);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setShowMainContent(false);
    }
  }, [loading]);

  const handleLoadingComplete = () => {
    setShowMainContent(true);
  };

  return (
    <>
      {/* 应用加载屏幕 */}
      <AppLoadingScreen
        isLoading={loading}
        onLoadingComplete={handleLoadingComplete}
      />

      {/* 主应用内容 */}
      <div className={`
        transition-all duration-300 ease-out
        ${showMainContent ? 'opacity-100' : 'opacity-0'}
      `}>
        <QueryClientProvider client={queryClient}>
          <Router
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true,
            }}
          >
            <Routes>
              <Route path="/test" element={
                <div className="min-h-screen bg-background p-8">
                  <TestComponent />
                </div>
              } />
              <Route path="/form-examples" element={
                <div className="min-h-screen bg-background">
                  <FormFieldExample />
                </div>
              } />
              <Route path="/card-examples" element={
                <div className="min-h-screen bg-background">
                  <CardExample />
                </div>
              } />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/auth-debug" element={<AuthDebugPage />} />
              <Route
                path="/"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <Navigate to="/dashboard" replace />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              <Route
                path="/dashboard"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <DashboardPage />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              <Route
                path="/form-examples"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <FormFieldExample />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              <Route
                path="/card-examples"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <CardExample />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              <Route
                path="/components"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <ComponentsPage />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              <Route
                path="/button-examples"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <ButtonExample />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              <Route
                path="/modal-examples"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <ModalExample />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              <Route
                path="/dropdown-examples"
                element={
                  <AuthGuard>
                    <MainLayout>
                      <DropdownExample />
                    </MainLayout>
                  </AuthGuard>
                }
              />
              {/* More routes will be added here */}
            </Routes>

            {/* 全局Toast通知容器 */}
            <ToastContainer />
          </Router>
        </QueryClientProvider>
      </div>
    </>
  );
}

export default App;
