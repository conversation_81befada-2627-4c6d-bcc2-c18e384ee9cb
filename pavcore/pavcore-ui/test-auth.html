<!DOCTYPE html>
<html>
<head>
    <title>认证状态测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>认证状态测试</h1>
    
    <div id="status"></div>
    
    <button onclick="checkStorage()">检查存储状态</button>
    <button onclick="setMockAuth()">设置模拟认证</button>
    <button onclick="clearAuth()">清除认证状态</button>
    <button onclick="testRefresh()">测试刷新</button>
    
    <div id="log"></div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(div);
            console.log(message);
        }

        function checkStorage() {
            log('检查存储状态...');
            
            // 检查 localStorage
            const token = localStorage.getItem('pavcore_token');
            const user = localStorage.getItem('pavcore_user');
            
            // 检查 zustand persist
            const zustandAuth = localStorage.getItem('pavcore-auth');
            
            log(`localStorage token: ${token ? '存在' : '不存在'}`);
            log(`localStorage user: ${user ? '存在' : '不存在'}`);
            log(`zustand persist: ${zustandAuth ? '存在' : '不存在'}`);
            
            if (zustandAuth) {
                try {
                    const parsed = JSON.parse(zustandAuth);
                    log(`zustand 认证状态: ${parsed.state?.isAuthenticated ? '已认证' : '未认证'}`);
                    log(`zustand token: ${parsed.state?.token ? '存在' : '不存在'}`);
                    log(`zustand user: ${parsed.state?.user ? '存在' : '不存在'}`);
                } catch (e) {
                    log('解析 zustand 状态失败', 'error');
                }
            }
        }

        function setMockAuth() {
            log('设置模拟认证状态...');
            
            const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTcwOTU1NjAwMCwiZXhwIjoyMDI1MDAwMDAwfQ.mock-signature';
            const mockUser = {
                id: 1,
                username: 'testuser',
                email: '<EMAIL>',
                nickname: '测试用户',
                enabled: true,
                roles: [],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z'
            };

            // 设置 localStorage
            localStorage.setItem('pavcore_token', mockToken);

            // 设置 zustand persist
            const zustandState = {
                state: {
                    token: mockToken,
                    refreshTokenValue: 'mock-refresh-token-' + Date.now(),
                    user: mockUser,
                    isAuthenticated: true,
                    permissions: ['read', 'write'],
                    menus: [],
                    encryptionSupported: true,
                    loading: false
                },
                version: 0
            };
            localStorage.setItem('pavcore-auth', JSON.stringify(zustandState));

            log('模拟认证状态已设置', 'success');
            checkStorage();
        }

        function clearAuth() {
            log('清除认证状态...');
            localStorage.removeItem('pavcore_token');
            localStorage.removeItem('pavcore_user');
            localStorage.removeItem('pavcore-auth');
            log('认证状态已清除', 'success');
            checkStorage();
        }

        function testRefresh() {
            log('3秒后将刷新页面测试状态恢复...', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }

        // 页面加载时检查状态
        window.onload = function() {
            log('页面已加载');
            checkStorage();
        };
    </script>
</body>
</html>
